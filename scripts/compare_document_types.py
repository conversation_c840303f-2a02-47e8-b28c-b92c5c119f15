#!/usr/bin/env python3
"""
Script to compare document types between constants files and database.
Checks for missing types and spelling inconsistencies.
"""

import sys
import os
from pathlib import Path
import json
from datetime import datetime
from difflib import SequenceMatcher

# Add project root to path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from src.shared.data_sources.db_models import DocumentType
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os

# Get database connection from environment or use defaults
SQL_SERVER = os.getenv('SQL_SERVER', 'localhost')
SQL_DATABASE = os.getenv('SQL_DATABASE', 'fileflow')
SQL_USERNAME = os.getenv('SQL_USERNAME', 'sa')
SQL_PASSWORD = os.getenv('SQL_PASSWORD', 'password')

def get_db_document_types():
    """Get document types directly from database."""
    try:
        # Create database connection
        connection_string = f"mssql+pyodbc://{SQL_USERNAME}:{SQL_PASSWORD}@{SQL_SERVER}/{SQL_DATABASE}?driver=ODBC+Driver+17+for+SQL+Server"
        engine = create_engine(connection_string)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        with SessionLocal() as session:
            document_types = session.query(DocumentType).all()
            return [doc_type.type for doc_type in document_types]
    except Exception as e:
        print(f"Error connecting to database: {e}")
        print("Please ensure database connection details are correct.")
        return []


def similarity(a, b):
    """Calculate similarity between two strings."""
    return SequenceMatcher(None, a.lower(), b.lower()).ratio()


def extract_efile_document_types():
    """Extract all document types from efile constants."""
    efile_types = set()
    
    # Import efile constants
    try:
        from src.classifier_service.efile.consts.pleading_definitions import PLEADING_DOCUMENT_DEFINITIONS
        for doc_type in PLEADING_DOCUMENT_DEFINITIONS.keys():
            efile_types.add(doc_type)
    except ImportError as e:
        print(f"Warning: Could not import pleading definitions: {e}")
    
    return efile_types


def extract_non_court_document_types():
    """Extract all document types from non-court constants."""
    non_court_types = set()
    
    # Import all non-court enum classes
    try:
        # Case & Claim Management
        from src.classifier_service.non_court_docs.consts.case_claim import (
            ClientIntakeAndProfile, CaseManagementAndWorkflow, 
            ClaimsResolutionsAndAgreements, FormalLegalAndSpecializedAdmin
        )
        
        # Financial
        from src.classifier_service.non_court_docs.consts.financial import (
            BankingAndAccounts, LoansAndCredit, InvestmentsAndRetirement,
            IncomeAndBenefitsFinancial, BillingInvoicesPayments, PropertyAndAssetManagement
        )
        
        # Medical
        from src.classifier_service.non_court_docs.consts.medical import (
            GovernmentBenefitAdministration, MedicalRecordsAndEvaluations, 
            HealthcareFinanceAndClaims
        )
        
        # Insurance
        from src.classifier_service.non_court_docs.consts.insurance import InsuranceSubType
        
        # Government
        from src.classifier_service.non_court_docs.consts.government import GovernmentAndAgencyDocuments
        
        # General Correspondence
        from src.classifier_service.non_court_docs.consts.general_correspondance import (
            GeneralCommunicationAndAdministrativeDocuments
        )
        
        # Notices
        from src.classifier_service.non_court_docs.consts.notices import NoticeDocuments
        
        # Extract values from all enums
        enum_classes = [
            ClientIntakeAndProfile, CaseManagementAndWorkflow, 
            ClaimsResolutionsAndAgreements, FormalLegalAndSpecializedAdmin,
            BankingAndAccounts, LoansAndCredit, InvestmentsAndRetirement,
            IncomeAndBenefitsFinancial, BillingInvoicesPayments, PropertyAndAssetManagement,
            GovernmentBenefitAdministration, MedicalRecordsAndEvaluations, 
            HealthcareFinanceAndClaims, InsuranceSubType, GovernmentAndAgencyDocuments,
            GeneralCommunicationAndAdministrativeDocuments, NoticeDocuments
        ]
        
        for enum_class in enum_classes:
            for doc_type in enum_class:
                non_court_types.add(doc_type.value)
                
    except ImportError as e:
        print(f"Warning: Could not import some non-court constants: {e}")
    
    return non_court_types


def find_potential_matches(missing_type, db_types, threshold=0.7):
    """Find potential spelling matches in database for a missing type."""
    matches = []
    for db_type in db_types:
        sim = similarity(missing_type, db_type)
        if sim >= threshold:
            matches.append((db_type, sim))
    return sorted(matches, key=lambda x: x[1], reverse=True)


def compare_document_types():
    """Main function to compare document types."""
    print("Starting document type comparison...")
    
    # Get document types from database
    db_types_list = get_db_document_types()
    if not db_types_list:
        print("Could not retrieve document types from database. Exiting.")
        return

    db_types = set(db_types_list)
    print(f"Found {len(db_types)} document types in database")
    
    # Get document types from constants
    efile_types = extract_efile_document_types()
    non_court_types = extract_non_court_document_types()
    
    print(f"Found {len(efile_types)} efile document types")
    print(f"Found {len(non_court_types)} non-court document types")
    
    # Compare efile types
    efile_missing = efile_types - db_types
    efile_potential_matches = {}
    
    for missing_type in efile_missing:
        matches = find_potential_matches(missing_type, db_types)
        if matches:
            efile_potential_matches[missing_type] = matches
    
    # Compare non-court types
    non_court_missing = non_court_types - db_types
    non_court_potential_matches = {}
    
    for missing_type in non_court_missing:
        matches = find_potential_matches(missing_type, db_types)
        if matches:
            non_court_potential_matches[missing_type] = matches
    
    # Find database types not in constants
    all_const_types = efile_types | non_court_types
    db_only_types = db_types - all_const_types
    
    # Generate report
    report = {
        "timestamp": datetime.now().isoformat(),
        "summary": {
            "total_db_types": len(db_types),
            "total_efile_types": len(efile_types),
            "total_non_court_types": len(non_court_types),
            "total_constants_types": len(all_const_types),
            "efile_missing_count": len(efile_missing),
            "non_court_missing_count": len(non_court_missing),
            "db_only_count": len(db_only_types)
        },
        "efile_analysis": {
            "missing_in_db": list(efile_missing),
            "potential_spelling_matches": efile_potential_matches
        },
        "non_court_analysis": {
            "missing_in_db": list(non_court_missing),
            "potential_spelling_matches": non_court_potential_matches
        },
        "database_only_types": list(db_only_types),
        "all_db_types": list(db_types),
        "all_efile_types": list(efile_types),
        "all_non_court_types": list(non_court_types)
    }
    
    return report


def print_report(report):
    """Print a formatted report to console."""
    print("\n" + "="*80)
    print("DOCUMENT TYPE COMPARISON REPORT")
    print("="*80)
    
    summary = report["summary"]
    print(f"\nSUMMARY:")
    print(f"  Database types: {summary['total_db_types']}")
    print(f"  EFile types: {summary['total_efile_types']}")
    print(f"  Non-court types: {summary['total_non_court_types']}")
    print(f"  Total constants types: {summary['total_constants_types']}")
    
    print(f"\nMISSING FROM DATABASE:")
    print(f"  EFile missing: {summary['efile_missing_count']}")
    print(f"  Non-court missing: {summary['non_court_missing_count']}")
    print(f"  Database only: {summary['db_only_count']}")
    
    # EFile missing types
    if report["efile_analysis"]["missing_in_db"]:
        print(f"\nEFILE TYPES MISSING FROM DATABASE ({len(report['efile_analysis']['missing_in_db'])}):")
        for missing_type in sorted(report["efile_analysis"]["missing_in_db"]):
            print(f"  - {missing_type}")
    
    # EFile potential matches
    if report["efile_analysis"]["potential_spelling_matches"]:
        print(f"\nEFILE POTENTIAL SPELLING MATCHES:")
        for missing_type, matches in report["efile_analysis"]["potential_spelling_matches"].items():
            print(f"  '{missing_type}' might match:")
            for match, similarity in matches[:3]:  # Show top 3 matches
                print(f"    - '{match}' (similarity: {similarity:.2f})")
    
    # Non-court missing types
    if report["non_court_analysis"]["missing_in_db"]:
        print(f"\nNON-COURT TYPES MISSING FROM DATABASE ({len(report['non_court_analysis']['missing_in_db'])}):")
        for missing_type in sorted(report["non_court_analysis"]["missing_in_db"]):
            print(f"  - {missing_type}")
    
    # Non-court potential matches
    if report["non_court_analysis"]["potential_spelling_matches"]:
        print(f"\nNON-COURT POTENTIAL SPELLING MATCHES:")
        for missing_type, matches in report["non_court_analysis"]["potential_spelling_matches"].items():
            print(f"  '{missing_type}' might match:")
            for match, similarity in matches[:3]:  # Show top 3 matches
                print(f"    - '{match}' (similarity: {similarity:.2f})")
    
    # Database only types
    if report["database_only_types"]:
        print(f"\nTYPES ONLY IN DATABASE ({len(report['database_only_types'])}):")
        for db_type in sorted(report["database_only_types"]):
            print(f"  - {db_type}")


def save_report(report, filename="document_types_comparison_report.json"):
    """Save the report to a JSON file."""
    with open(filename, 'w') as f:
        json.dump(report, f, indent=2)
    print(f"\nDetailed report saved to: {filename}")


if __name__ == "__main__":
    try:
        report = compare_document_types()
        print_report(report)
        save_report(report)
        
        print("\n" + "="*80)
        print("COMPARISON COMPLETE")
        print("="*80)
        
    except Exception as e:
        print(f"Error during comparison: {e}")
        import traceback
        traceback.print_exc()
